# تحديثات نظام إخفاء الـ HUD عند الموت والإنعاش

## التغييرات المضافة

تم إضافة نظام متكامل لإخفاء الـ HUD عند موت اللاعب وإظهاره عند الإنعاش.

### الميزات الجديدة:

1. **إخفاء تلقائي للـ HUD عند الموت**
   - يتم إخفاء الـ HUD فوراً عند موت اللاعب
   - يعمل مع جميع أنواع الموت (قتل، انتحار، إلخ)

2. **إظهار تلقائي للـ HUD عند الإنعاش**
   - يتم إظهار الـ HUD فوراً عند إنعاش اللاعب
   - يعمل مع الإنعاش العادي والإنعاش الجماعي

3. **نظام احتياطي للتحقق**
   - Thread يعمل في الخلفية للتحقق من حالة الموت
   - يضمن عمل النظام حتى لو فشلت الـ events

### الـ Events المدعومة:

- `esx_misc:hidehud` - Event رئيسي للتحكم في الـ HUD
- `esx:onPlayerDeath` - عند موت اللاعب
- `esx_ambulancejob:revive` - عند إنعاش اللاعب
- `esx_ambulancejob:revive_alldeads` - عند الإنعاش الجماعي
- `esx:onPlayerSpawn` - عند spawn اللاعب
- `idry_hud:morto` - للتوافق مع الأنظمة القديمة

### Export Function:

```lua
-- لإخفاء الـ HUD
exports['rsx_hud']:SetDeathHudVisibility(true)

-- لإظهار الـ HUD
exports['rsx_hud']:SetDeathHudVisibility(false)
```

### كيفية العمل:

1. عند موت اللاعب، يتم استدعاء `TriggerEvent('codem-blackhudv2:SetForceHide', true)`
2. هذا يؤدي إلى إخفاء الـ HUD بالكامل
3. عند الإنعاش، يتم استدعاء `TriggerEvent('codem-blackhudv2:SetForceHide', false)`
4. هذا يؤدي إلى إظهار الـ HUD مرة أخرى

### التوافق:

- متوافق مع ESX Framework
- متوافق مع esx_ambulancejob
- متوافق مع الأنظمة القديمة
- لا يؤثر على باقي وظائف الـ HUD

## ملاحظات مهمة:

- النظام يعمل تلقائياً ولا يحتاج إلى تدخل يدوي
- يمكن التحكم فيه من خلال Export Function إذا لزم الأمر
- Thread الاحتياطي يضمن عمل النظام في جميع الحالات
- تم الحفاظ على جميع الوظائف الأصلية للـ HUD

## التحديث الثاني - إصلاح مشكلة ظهور الـ HUD مرة أخرى:

### المشاكل التي تم إصلاحها:

1. **Thread الـ Pause Menu**: تم تعديله لعدم إظهار الـ HUD إذا كان اللاعب ميت
2. **Thread الـ EnableHUD**: تم تعديله لعدم إظهار الـ HUD إذا كان اللاعب ميت
3. **Event الـ esx:onPlayerSpawn**: تم تعطيله لأنه كان يظهر الـ HUD عند الـ spawn حتى لو كان اللاعب ميت
4. **Thread الاحتياطي**: تم تحسينه ليقوم فقط بإخفاء الـ HUD عند الموت وليس إظهاره
5. **UpdateSettings Handler**: تم تعديله للتأكد من أن forceHide يبقى true إذا كان اللاعب ميت
6. **SetForceHide Handler**: تم إضافة فحص لمنع إظهار الـ HUD إذا كان اللاعب ميت
7. **Loaded Handler**: تم تحسينه لفحص حالة اللاعب عند التحميل

### النتيجة:
الآن الـ HUD سيختفي عند الموت ولن يظهر مرة أخرى إلا عند الإنعاش فقط.

## التاريخ:
- تم الإنشاء في: 2025-06-29
- تم التحديث في: 2025-06-29 (إصلاح مشكلة الظهور المتكرر)
