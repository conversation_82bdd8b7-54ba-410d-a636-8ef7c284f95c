2025/06/29-21:47:21.614563 1cc0 RocksDB version: 8.3.0
2025/06/29-21:47:21.614854 1cc0 DB SUMMARY
2025/06/29-21:47:21.614886 1cc0 DB Session ID:  SYS2P0CDJJB53MC31RRJ
2025/06/29-21:47:21.616849 1cc0 CURRENT file:  CURRENT
2025/06/29-21:47:21.616894 1cc0 IDENTITY file:  IDENTITY
2025/06/29-21:47:21.616993 1cc0 MANIFEST file:  MANIFEST-000268 size: 681 Bytes
2025/06/29-21:47:21.617014 1cc0 SST files in E:\napoly\server-data\db\default dir, Total Num: 3, files: 000258.sst 000261.sst 000266.sst 
2025/06/29-21:47:21.617029 1cc0 Write Ahead Log file in E:\napoly\server-data\db\default: 000267.log size: 3672 ; 
2025/06/29-21:47:21.617044 1cc0                         Options.error_if_exists: 0
2025/06/29-21:47:21.617060 1cc0                       Options.create_if_missing: 1
2025/06/29-21:47:21.617491 1cc0                         Options.paranoid_checks: 1
2025/06/29-21:47:21.617506 1cc0             Options.flush_verify_memtable_count: 1
2025/06/29-21:47:21.617512 1cc0                               Options.track_and_verify_wals_in_manifest: 0
2025/06/29-21:47:21.617516 1cc0        Options.verify_sst_unique_id_in_manifest: 1
2025/06/29-21:47:21.617521 1cc0                                     Options.env: 0000021110261D80
2025/06/29-21:47:21.617526 1cc0                                      Options.fs: WinFS
2025/06/29-21:47:21.617530 1cc0                                Options.info_log: 000002111323A110
2025/06/29-21:47:21.617535 1cc0                Options.max_file_opening_threads: 16
2025/06/29-21:47:21.617539 1cc0                              Options.statistics: 0000000000000000
2025/06/29-21:47:21.617543 1cc0                               Options.use_fsync: 0
2025/06/29-21:47:21.617548 1cc0                       Options.max_log_file_size: 0
2025/06/29-21:47:21.617552 1cc0                  Options.max_manifest_file_size: 1073741824
2025/06/29-21:47:21.617557 1cc0                   Options.log_file_time_to_roll: 0
2025/06/29-21:47:21.617562 1cc0                       Options.keep_log_file_num: 10
2025/06/29-21:47:21.617566 1cc0                    Options.recycle_log_file_num: 0
2025/06/29-21:47:21.617571 1cc0                         Options.allow_fallocate: 1
2025/06/29-21:47:21.617575 1cc0                        Options.allow_mmap_reads: 0
2025/06/29-21:47:21.617579 1cc0                       Options.allow_mmap_writes: 0
2025/06/29-21:47:21.617584 1cc0                        Options.use_direct_reads: 0
2025/06/29-21:47:21.617588 1cc0                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/29-21:47:21.617593 1cc0          Options.create_missing_column_families: 0
2025/06/29-21:47:21.617597 1cc0                              Options.db_log_dir: 
2025/06/29-21:47:21.617601 1cc0                                 Options.wal_dir: 
2025/06/29-21:47:21.617606 1cc0                Options.table_cache_numshardbits: 6
2025/06/29-21:47:21.617610 1cc0                         Options.WAL_ttl_seconds: 0
2025/06/29-21:47:21.617614 1cc0                       Options.WAL_size_limit_MB: 0
2025/06/29-21:47:21.617619 1cc0                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/29-21:47:21.617623 1cc0             Options.manifest_preallocation_size: 4194304
2025/06/29-21:47:21.617628 1cc0                     Options.is_fd_close_on_exec: 1
2025/06/29-21:47:21.617632 1cc0                   Options.advise_random_on_open: 1
2025/06/29-21:47:21.617637 1cc0                    Options.db_write_buffer_size: 0
2025/06/29-21:47:21.617641 1cc0                    Options.write_buffer_manager: 0000021110262050
2025/06/29-21:47:21.617646 1cc0         Options.access_hint_on_compaction_start: 1
2025/06/29-21:47:21.617650 1cc0           Options.random_access_max_buffer_size: 1048576
2025/06/29-21:47:21.617654 1cc0                      Options.use_adaptive_mutex: 0
2025/06/29-21:47:21.617659 1cc0                            Options.rate_limiter: 0000000000000000
2025/06/29-21:47:21.617664 1cc0     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/29-21:47:21.617668 1cc0                       Options.wal_recovery_mode: 2
2025/06/29-21:47:21.617717 1cc0                  Options.enable_thread_tracking: 0
2025/06/29-21:47:21.617726 1cc0                  Options.enable_pipelined_write: 0
2025/06/29-21:47:21.617731 1cc0                  Options.unordered_write: 0
2025/06/29-21:47:21.617736 1cc0         Options.allow_concurrent_memtable_write: 1
2025/06/29-21:47:21.617740 1cc0      Options.enable_write_thread_adaptive_yield: 1
2025/06/29-21:47:21.617745 1cc0             Options.write_thread_max_yield_usec: 100
2025/06/29-21:47:21.617749 1cc0            Options.write_thread_slow_yield_usec: 3
2025/06/29-21:47:21.617754 1cc0                               Options.row_cache: None
2025/06/29-21:47:21.617758 1cc0                              Options.wal_filter: None
2025/06/29-21:47:21.617763 1cc0             Options.avoid_flush_during_recovery: 0
2025/06/29-21:47:21.617767 1cc0             Options.allow_ingest_behind: 0
2025/06/29-21:47:21.617771 1cc0             Options.two_write_queues: 0
2025/06/29-21:47:21.617775 1cc0             Options.manual_wal_flush: 0
2025/06/29-21:47:21.617780 1cc0             Options.wal_compression: 0
2025/06/29-21:47:21.617784 1cc0             Options.atomic_flush: 0
2025/06/29-21:47:21.617788 1cc0             Options.avoid_unnecessary_blocking_io: 0
2025/06/29-21:47:21.617793 1cc0                 Options.persist_stats_to_disk: 0
2025/06/29-21:47:21.617797 1cc0                 Options.write_dbid_to_manifest: 0
2025/06/29-21:47:21.617801 1cc0                 Options.log_readahead_size: 0
2025/06/29-21:47:21.617806 1cc0                 Options.file_checksum_gen_factory: Unknown
2025/06/29-21:47:21.617811 1cc0                 Options.best_efforts_recovery: 0
2025/06/29-21:47:21.617815 1cc0                Options.max_bgerror_resume_count: 2147483647
2025/06/29-21:47:21.617819 1cc0            Options.bgerror_resume_retry_interval: 1000000
2025/06/29-21:47:21.617824 1cc0             Options.allow_data_in_errors: 0
2025/06/29-21:47:21.617828 1cc0             Options.db_host_id: __hostname__
2025/06/29-21:47:21.617832 1cc0             Options.enforce_single_del_contracts: true
2025/06/29-21:47:21.617837 1cc0             Options.max_background_jobs: 2
2025/06/29-21:47:21.617841 1cc0             Options.max_background_compactions: -1
2025/06/29-21:47:21.617845 1cc0             Options.max_subcompactions: 1
2025/06/29-21:47:21.617850 1cc0             Options.avoid_flush_during_shutdown: 0
2025/06/29-21:47:21.617854 1cc0           Options.writable_file_max_buffer_size: 1048576
2025/06/29-21:47:21.617858 1cc0             Options.delayed_write_rate : 16777216
2025/06/29-21:47:21.617863 1cc0             Options.max_total_wal_size: 0
2025/06/29-21:47:21.617867 1cc0             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/29-21:47:21.617871 1cc0                   Options.stats_dump_period_sec: 600
2025/06/29-21:47:21.617875 1cc0                 Options.stats_persist_period_sec: 600
2025/06/29-21:47:21.617880 1cc0                 Options.stats_history_buffer_size: 1048576
2025/06/29-21:47:21.617884 1cc0                          Options.max_open_files: -1
2025/06/29-21:47:21.617888 1cc0                          Options.bytes_per_sync: 0
2025/06/29-21:47:21.617892 1cc0                      Options.wal_bytes_per_sync: 0
2025/06/29-21:47:21.617896 1cc0                   Options.strict_bytes_per_sync: 0
2025/06/29-21:47:21.617901 1cc0       Options.compaction_readahead_size: 0
2025/06/29-21:47:21.617905 1cc0                  Options.max_background_flushes: -1
2025/06/29-21:47:21.617909 1cc0 Compression algorithms supported:
2025/06/29-21:47:21.617922 1cc0 	kZSTD supported: 0
2025/06/29-21:47:21.617927 1cc0 	kSnappyCompression supported: 0
2025/06/29-21:47:21.617931 1cc0 	kBZip2Compression supported: 0
2025/06/29-21:47:21.617936 1cc0 	kZlibCompression supported: 1
2025/06/29-21:47:21.617940 1cc0 	kLZ4Compression supported: 1
2025/06/29-21:47:21.617944 1cc0 	kXpressCompression supported: 0
2025/06/29-21:47:21.617949 1cc0 	kLZ4HCCompression supported: 1
2025/06/29-21:47:21.617953 1cc0 	kZSTDNotFinalCompression supported: 0
2025/06/29-21:47:21.617997 1cc0 Fast CRC32 supported: Not supported on x86
2025/06/29-21:47:21.618005 1cc0 DMutex implementation: std::mutex
2025/06/29-21:47:21.619369 1cc0 [db\version_set.cc:5791] Recovering from manifest file: E:\napoly\server-data\db\default/MANIFEST-000268
2025/06/29-21:47:21.619603 1cc0 [db\column_family.cc:621] --------------- Options for column family [default]:
2025/06/29-21:47:21.619613 1cc0               Options.comparator: leveldb.BytewiseComparator
2025/06/29-21:47:21.619616 1cc0           Options.merge_operator: None
2025/06/29-21:47:21.619619 1cc0        Options.compaction_filter: None
2025/06/29-21:47:21.619622 1cc0        Options.compaction_filter_factory: None
2025/06/29-21:47:21.619625 1cc0  Options.sst_partitioner_factory: None
2025/06/29-21:47:21.619628 1cc0         Options.memtable_factory: SkipListFactory
2025/06/29-21:47:21.619631 1cc0            Options.table_factory: BlockBasedTable
2025/06/29-21:47:21.619654 1cc0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002111580A830)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0000021110261F70
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-21:47:21.619658 1cc0        Options.write_buffer_size: 67108864
2025/06/29-21:47:21.619661 1cc0  Options.max_write_buffer_number: 2
2025/06/29-21:47:21.619664 1cc0          Options.compression: LZ4
2025/06/29-21:47:21.619667 1cc0                  Options.bottommost_compression: Disabled
2025/06/29-21:47:21.619670 1cc0       Options.prefix_extractor: nullptr
2025/06/29-21:47:21.619673 1cc0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-21:47:21.619676 1cc0             Options.num_levels: 7
2025/06/29-21:47:21.619678 1cc0        Options.min_write_buffer_number_to_merge: 1
2025/06/29-21:47:21.619681 1cc0     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-21:47:21.619684 1cc0     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-21:47:21.619687 1cc0            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-21:47:21.619690 1cc0                  Options.bottommost_compression_opts.level: 32767
2025/06/29-21:47:21.619693 1cc0               Options.bottommost_compression_opts.strategy: 0
2025/06/29-21:47:21.619696 1cc0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-21:47:21.619699 1cc0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-21:47:21.619701 1cc0         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-21:47:21.619704 1cc0                  Options.bottommost_compression_opts.enabled: false
2025/06/29-21:47:21.619707 1cc0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-21:47:21.619710 1cc0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-21:47:21.619713 1cc0            Options.compression_opts.window_bits: -14
2025/06/29-21:47:21.619716 1cc0                  Options.compression_opts.level: 32767
2025/06/29-21:47:21.619719 1cc0               Options.compression_opts.strategy: 0
2025/06/29-21:47:21.619722 1cc0         Options.compression_opts.max_dict_bytes: 0
2025/06/29-21:47:21.619728 1cc0         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-21:47:21.619732 1cc0         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-21:47:21.619735 1cc0         Options.compression_opts.parallel_threads: 1
2025/06/29-21:47:21.619737 1cc0                  Options.compression_opts.enabled: false
2025/06/29-21:47:21.619740 1cc0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-21:47:21.619743 1cc0      Options.level0_file_num_compaction_trigger: 4
2025/06/29-21:47:21.619746 1cc0          Options.level0_slowdown_writes_trigger: 20
2025/06/29-21:47:21.619749 1cc0              Options.level0_stop_writes_trigger: 36
2025/06/29-21:47:21.619752 1cc0                   Options.target_file_size_base: 67108864
2025/06/29-21:47:21.619755 1cc0             Options.target_file_size_multiplier: 1
2025/06/29-21:47:21.619758 1cc0                Options.max_bytes_for_level_base: 268435456
2025/06/29-21:47:21.619761 1cc0 Options.level_compaction_dynamic_level_bytes: 0
2025/06/29-21:47:21.619764 1cc0          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-21:47:21.619767 1cc0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-21:47:21.619770 1cc0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-21:47:21.619773 1cc0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-21:47:21.619776 1cc0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-21:47:21.619778 1cc0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-21:47:21.619781 1cc0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-21:47:21.619784 1cc0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-21:47:21.619787 1cc0       Options.max_sequential_skip_in_iterations: 8
2025/06/29-21:47:21.619790 1cc0                    Options.max_compaction_bytes: 1677721600
2025/06/29-21:47:21.619793 1cc0   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-21:47:21.619796 1cc0                        Options.arena_block_size: 1048576
2025/06/29-21:47:21.619798 1cc0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-21:47:21.619801 1cc0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-21:47:21.619804 1cc0                Options.disable_auto_compactions: 0
2025/06/29-21:47:21.619810 1cc0                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-21:47:21.619813 1cc0                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-21:47:21.619816 1cc0 Options.compaction_options_universal.size_ratio: 1
2025/06/29-21:47:21.619819 1cc0 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-21:47:21.619822 1cc0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-21:47:21.619825 1cc0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-21:47:21.619828 1cc0 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-21:47:21.619831 1cc0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-21:47:21.619834 1cc0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-21:47:21.619837 1cc0 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-21:47:21.619841 1cc0                   Options.table_properties_collectors: 
2025/06/29-21:47:21.619845 1cc0                   Options.inplace_update_support: 0
2025/06/29-21:47:21.619847 1cc0                 Options.inplace_update_num_locks: 10000
2025/06/29-21:47:21.619850 1cc0               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-21:47:21.619853 1cc0               Options.memtable_whole_key_filtering: 0
2025/06/29-21:47:21.619856 1cc0   Options.memtable_huge_page_size: 0
2025/06/29-21:47:21.619859 1cc0                           Options.bloom_locality: 0
2025/06/29-21:47:21.619862 1cc0                    Options.max_successive_merges: 0
2025/06/29-21:47:21.619865 1cc0                Options.optimize_filters_for_hits: 0
2025/06/29-21:47:21.619905 1cc0                Options.paranoid_file_checks: 0
2025/06/29-21:47:21.619910 1cc0                Options.force_consistency_checks: 1
2025/06/29-21:47:21.619913 1cc0                Options.report_bg_io_stats: 0
2025/06/29-21:47:21.619915 1cc0                               Options.ttl: 2592000
2025/06/29-21:47:21.619918 1cc0          Options.periodic_compaction_seconds: 0
2025/06/29-21:47:21.619921 1cc0  Options.preclude_last_level_data_seconds: 0
2025/06/29-21:47:21.619924 1cc0    Options.preserve_internal_time_seconds: 0
2025/06/29-21:47:21.619927 1cc0                       Options.enable_blob_files: false
2025/06/29-21:47:21.619930 1cc0                           Options.min_blob_size: 0
2025/06/29-21:47:21.619933 1cc0                          Options.blob_file_size: 268435456
2025/06/29-21:47:21.619936 1cc0                   Options.blob_compression_type: NoCompression
2025/06/29-21:47:21.619939 1cc0          Options.enable_blob_garbage_collection: false
2025/06/29-21:47:21.619942 1cc0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-21:47:21.619946 1cc0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-21:47:21.619949 1cc0          Options.blob_compaction_readahead_size: 0
2025/06/29-21:47:21.619952 1cc0                Options.blob_file_starting_level: 0
2025/06/29-21:47:21.619955 1cc0 Options.experimental_mempurge_threshold: 0.000000
2025/06/29-21:47:21.623866 1cc0 [db\version_set.cc:5842] Recovered from manifest file:E:\napoly\server-data\db\default/MANIFEST-000268 succeeded,manifest_file_number is 268, next_file_number is 270, last_sequence is 795, log_number is 263,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 263
2025/06/29-21:47:21.623904 1cc0 [db\version_set.cc:5851] Column family [default] (ID 0), log number is 263
2025/06/29-21:47:21.624406 1cc0 [db\db_impl\db_impl_open.cc:636] DB ID: 1ac3d8dc-4830-11f0-b4ab-244bfe56a7b1
2025/06/29-21:47:21.628513 1cc0 EVENT_LOG_v1 {"time_micros": 1751219241628503, "job": 1, "event": "recovery_started", "wal_files": [267]}
2025/06/29-21:47:21.628536 1cc0 [db\db_impl\db_impl_open.cc:1131] Recovering log #267 mode 2
2025/06/29-21:47:21.671720 1cc0 EVENT_LOG_v1 {"time_micros": 1751219241671667, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 271, "file_size": 1375, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 824, "largest_seqno": 837, "table_properties": {"data_size": 347, "index_size": 74, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 951, "raw_average_key_size": 67, "raw_value_size": 77, "raw_average_value_size": 5, "num_data_blocks": 1, "num_entries": 14, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "LZ4", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751219241, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "1ac3d8dc-4830-11f0-b4ab-244bfe56a7b1", "db_session_id": "SYS2P0CDJJB53MC31RRJ", "orig_file_number": 271, "seqno_to_time_mapping": "N/A"}}
2025/06/29-21:47:21.682007 1cc0 EVENT_LOG_v1 {"time_micros": 1751219241681993, "job": 1, "event": "recovery_finished"}
2025/06/29-21:47:21.682595 1cc0 [db\version_set.cc:5304] Creating manifest 273
2025/06/29-21:47:21.803416 1cc0 [file\delete_scheduler.cc:77] Deleted file E:\napoly\server-data\db\default/000267.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-21:47:21.803456 1cc0 [db\db_impl\db_impl_files.cc:654] [JOB 2] Delete info log file E:\napoly\server-data\db\default//LOG.old.1751153756053636
2025/06/29-21:47:21.803811 1cc0 [db\db_impl\db_impl_open.cc:2085] SstFileManager instance 000002111543A8F0
2025/06/29-21:47:21.805804 1cc0 DB pointer 0000021115990040
2025/06/29-21:47:21.807114 5a24 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-21:47:21.807133 5a24 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 0.2 total, 0.2 interval
Cumulative writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    4.03 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.043       0      0       0.0       0.0
  L1      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.38 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.043       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.043       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.04              0.00         1    0.043       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.2 total, 0.2 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000021110261F70#11860 capacity: 32.00 MB seed: 2116340078 usage: 0.91 KB table_size: 1024 occupancy: 2 collections: 1 last_copies: 0 last_secs: 0.00011 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.74 KB,0.0022471%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/29-21:57:21.808175 5a24 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-21:57:21.808197 5a24 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 600.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 13 writes, 13 keys, 13 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    4.03 KB   0.8      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.043       0      0       0.0       0.0
  L1      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      4/0    5.38 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.043       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.04              0.00         1    0.043       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 600.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0000021110261F70#11860 capacity: 32.00 MB seed: 2116340078 usage: 0.91 KB table_size: 1024 occupancy: 2 collections: 2 last_copies: 0 last_secs: 5.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.74 KB,0.0022471%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
