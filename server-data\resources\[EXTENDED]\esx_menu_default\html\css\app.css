@font-face {
  font-family: bankgothic;
  src: url('../fonts/bankgothic.ttf');
}

@font-face {
  font-family: pcdown;
  src: url('../fonts/pdown.ttf');
}

@font-face {
  font-family: RB-Bold ;
  src: url('../fonts/RB-Bold.ttf');
}

.menu {
  font-family: RB-Bold;
  font-weight: bold;
  font-size  : 18px;  
  min-width  : 400px;
  color      : #fff;
  box-shadow : 0px 0px 50px 0px #000;
  position   : absolute;
  direction: rtl;
}

.menu.align-left {
  left: 40;
  top : 50%;
  transform: translate(0, -50%); 
}

.menu.align-top-left {
  right : 40;
  bottom: 110;
}

.menu.align-top {
  left: 50%;
  top : 40;
  transform: translate(-50%, 0); 
}

.menu.align-top-right {
  right : 40;
  bottom: 110;
}

.menu.align-right {
  right: 40;
  top  : 50%;
  transform: translate(0, -50%); 
}

.menu.align-bottom-right {
  right : 40;
  bottom: 110;
}

.menu.align-bottom {
  left  : 50%;
  bottom: 40;
  transform: translate(-50%, 0); 
}

.menu.align-bottom-left {
  right : 40;
  bottom: 110;
}

.menu.align-center {
  left : 50%;
  top  : 50%;
  transform: translate(-50%, -50%); 
}

.menu .head {
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  height: 65px;
  line-height: 65px;
  color: #ffffff;
  background-color: rgba(0, 0, 0, 0.9);
  text-shadow:
       3px 3px 0 #000,
     -1px -1px 0 #000,  
      1px -1px 0 #000,
      -1px 1px 0 #000,
       1px 1px 0 #000;
  position: relative;
  overflow: hidden;
  border-bottom: 2px solid rgba(255, 185, 8, 0.6);
}

.menu .head::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(to right, transparent, rgba(255, 185, 8, 0.8), transparent);
  box-shadow: 0 0 10px 1px rgba(255, 185, 8, 0.5);
}

.menu .head-logo {
  display: none;
}

.menu .head-logo img {
  display: none;
}

.menu .head span {
  display: inline-block;
  padding: 0 15px;
  position: relative;
}

.menu .head span::after {
  content: "";
  position: absolute;
  bottom: 15px;
  left: 50%;
  width: 70%;
  height: 2px;
  transform: translateX(-50%);
  background: linear-gradient(to right, transparent, rgba(255, 185, 8, 0.6), transparent);
}

.menu .menu-items {
	max-height : 600px;
	overflow-y : hidden;
	/*background: -webkit-linear-gradient(left, transparent 0%, rgb(90, 90, 90) 100%); */
  animation: shake 5.0s linear forwards;
}

.menu .menu-items .menu-item {
	display         : block;
	height          : 100%;
	line-height     : 35px;
	text-align      : center;
	color           : #F3F3F3;
	text-shadow     :
                    1px 1px 0 #000,
                   -1px -1px 0 #000,  
                    1px -1px 0 #000,
                   -1px 1px 0 #000,
                    1px 1px 0 #000;
	/*background-color: rgba(45, 45, 45, .8);*/			
    background-color: rgba(0, 0, 0, 0.6);		
}

.menu .menu-items .menu-item.selected {
  /*background: -webkit-linear-gradient(left, transparent 0%, rgb(220, 12, 12) 100%);*/
  background: linear-gradient(to bottom right, #ffb90881 ,#ffb90881 20%);
  /*background-color: rgba(0, 162, 255, .7);*/
}

@keyframes shake {
  5%, 15%, 25%, 35%, 55%, 65%, 75%, 95% {
      filter: blur(0.018em);
      text-shadow: 
          0 0 0 #0c2ffb, 
          0 0 0 #2cfcfd, 
          0 0 0 #fb203b, 
          0 0 0 #fefc4b;        

  }

  10%, 30%, 40%, 50%, 70%, 80%, 90% {
      filter: blur(0.01em);
      text-shadow: 
          0 0.125em 0 #0c2ffb, 
          0 0.25em 0 #2cfcfd, 
          0 -0.125em 0 #fb203b, 
          0 -0.25em 0 #fefc4b;        

  }

  20%, 60% {
      filter: blur(0.03em);
      text-shadow: 
          0 -0.0625em 0 #0c2ffb, 
          0 -0.125em 0 #2cfcfd, 
          0 0.0625em 0 #fb203b, 
          0 0.125em 0 #fefc4b;        

  }

  45%, 85% {
      filter: blur(0.03em);
      text-shadow: 
          0 0.03125em 0 #0c2ffb, 
          0 0.0625em 0 #2cfcfd, 
          0 -0.03125em 0 #fb203b, 
          0 -0.0625em 0 #fefc4b;        

  }

  100% {
      filter: blur(0.007em);
      text-shadow: 
          0 0 0 #0c2ffb, 
          0 0 0 #2cfcfd, 
          0 0 0 #fb203b, 
          0 0 0 #fefc4b;        

  }
}

.menu-divider {
  height: 0.8px;
  background: linear-gradient(to right, transparent, rgba(255, 185, 8, 0.4), transparent);
  margin: 0.5px 18px;
  border-radius: 0.3px;
  box-shadow: 0 0 2px rgba(255, 185, 8, 0.15);
}

/* Fix to avoid double dividers */
.menu-item:last-child + .menu-divider {
  display: none;
}
