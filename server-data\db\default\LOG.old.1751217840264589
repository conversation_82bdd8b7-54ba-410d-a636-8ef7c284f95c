2025/06/29-18:32:55.591918 1c80 RocksDB version: 8.3.0
2025/06/29-18:32:55.592023 1c80 DB SUMMARY
2025/06/29-18:32:55.592049 1c80 DB Session ID:  STZ3IMGWS96CL0XMJTM4
2025/06/29-18:32:55.610643 1c80 CURRENT file:  CURRENT
2025/06/29-18:32:55.610720 1c80 IDENTITY file:  IDENTITY
2025/06/29-18:32:55.610865 1c80 MANIFEST file:  MANIFEST-000250 size: 866 Bytes
2025/06/29-18:32:55.610886 1c80 SST files in E:\napoly\server-data\db\default dir, Total Num: 4, files: 000235.sst 000238.sst 000243.sst 000248.sst 
2025/06/29-18:32:55.610903 1c80 Write Ahead Log file in E:\napoly\server-data\db\default: 000249.log size: 2448 ; 
2025/06/29-18:32:55.610921 1c80                         Options.error_if_exists: 0
2025/06/29-18:32:55.610935 1c80                       Options.create_if_missing: 1
2025/06/29-18:32:55.611446 1c80                         Options.paranoid_checks: 1
2025/06/29-18:32:55.611461 1c80             Options.flush_verify_memtable_count: 1
2025/06/29-18:32:55.611466 1c80                               Options.track_and_verify_wals_in_manifest: 0
2025/06/29-18:32:55.611471 1c80        Options.verify_sst_unique_id_in_manifest: 1
2025/06/29-18:32:55.611476 1c80                                     Options.env: 000002007EAF65E0
2025/06/29-18:32:55.611481 1c80                                      Options.fs: WinFS
2025/06/29-18:32:55.611486 1c80                                Options.info_log: 0000020073824C90
2025/06/29-18:32:55.611490 1c80                Options.max_file_opening_threads: 16
2025/06/29-18:32:55.611495 1c80                              Options.statistics: 0000000000000000
2025/06/29-18:32:55.611499 1c80                               Options.use_fsync: 0
2025/06/29-18:32:55.611503 1c80                       Options.max_log_file_size: 0
2025/06/29-18:32:55.611506 1c80                  Options.max_manifest_file_size: 1073741824
2025/06/29-18:32:55.611510 1c80                   Options.log_file_time_to_roll: 0
2025/06/29-18:32:55.611514 1c80                       Options.keep_log_file_num: 10
2025/06/29-18:32:55.611518 1c80                    Options.recycle_log_file_num: 0
2025/06/29-18:32:55.611521 1c80                         Options.allow_fallocate: 1
2025/06/29-18:32:55.611525 1c80                        Options.allow_mmap_reads: 0
2025/06/29-18:32:55.611529 1c80                       Options.allow_mmap_writes: 0
2025/06/29-18:32:55.611533 1c80                        Options.use_direct_reads: 0
2025/06/29-18:32:55.611537 1c80                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/29-18:32:55.611541 1c80          Options.create_missing_column_families: 0
2025/06/29-18:32:55.611545 1c80                              Options.db_log_dir: 
2025/06/29-18:32:55.611548 1c80                                 Options.wal_dir: 
2025/06/29-18:32:55.611553 1c80                Options.table_cache_numshardbits: 6
2025/06/29-18:32:55.611557 1c80                         Options.WAL_ttl_seconds: 0
2025/06/29-18:32:55.611562 1c80                       Options.WAL_size_limit_MB: 0
2025/06/29-18:32:55.611566 1c80                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/29-18:32:55.611570 1c80             Options.manifest_preallocation_size: 4194304
2025/06/29-18:32:55.611574 1c80                     Options.is_fd_close_on_exec: 1
2025/06/29-18:32:55.611578 1c80                   Options.advise_random_on_open: 1
2025/06/29-18:32:55.611582 1c80                    Options.db_write_buffer_size: 0
2025/06/29-18:32:55.611586 1c80                    Options.write_buffer_manager: 000002007EAF66D0
2025/06/29-18:32:55.611590 1c80         Options.access_hint_on_compaction_start: 1
2025/06/29-18:32:55.611594 1c80           Options.random_access_max_buffer_size: 1048576
2025/06/29-18:32:55.611598 1c80                      Options.use_adaptive_mutex: 0
2025/06/29-18:32:55.611602 1c80                            Options.rate_limiter: 0000000000000000
2025/06/29-18:32:55.611608 1c80     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/29-18:32:55.611612 1c80                       Options.wal_recovery_mode: 2
2025/06/29-18:32:55.611668 1c80                  Options.enable_thread_tracking: 0
2025/06/29-18:32:55.611678 1c80                  Options.enable_pipelined_write: 0
2025/06/29-18:32:55.611683 1c80                  Options.unordered_write: 0
2025/06/29-18:32:55.611687 1c80         Options.allow_concurrent_memtable_write: 1
2025/06/29-18:32:55.611691 1c80      Options.enable_write_thread_adaptive_yield: 1
2025/06/29-18:32:55.611695 1c80             Options.write_thread_max_yield_usec: 100
2025/06/29-18:32:55.611699 1c80            Options.write_thread_slow_yield_usec: 3
2025/06/29-18:32:55.611703 1c80                               Options.row_cache: None
2025/06/29-18:32:55.611707 1c80                              Options.wal_filter: None
2025/06/29-18:32:55.611711 1c80             Options.avoid_flush_during_recovery: 0
2025/06/29-18:32:55.611716 1c80             Options.allow_ingest_behind: 0
2025/06/29-18:32:55.611720 1c80             Options.two_write_queues: 0
2025/06/29-18:32:55.611724 1c80             Options.manual_wal_flush: 0
2025/06/29-18:32:55.611728 1c80             Options.wal_compression: 0
2025/06/29-18:32:55.611732 1c80             Options.atomic_flush: 0
2025/06/29-18:32:55.611737 1c80             Options.avoid_unnecessary_blocking_io: 0
2025/06/29-18:32:55.611741 1c80                 Options.persist_stats_to_disk: 0
2025/06/29-18:32:55.611745 1c80                 Options.write_dbid_to_manifest: 0
2025/06/29-18:32:55.611749 1c80                 Options.log_readahead_size: 0
2025/06/29-18:32:55.611754 1c80                 Options.file_checksum_gen_factory: Unknown
2025/06/29-18:32:55.611758 1c80                 Options.best_efforts_recovery: 0
2025/06/29-18:32:55.611762 1c80                Options.max_bgerror_resume_count: 2147483647
2025/06/29-18:32:55.611767 1c80            Options.bgerror_resume_retry_interval: 1000000
2025/06/29-18:32:55.611771 1c80             Options.allow_data_in_errors: 0
2025/06/29-18:32:55.611775 1c80             Options.db_host_id: __hostname__
2025/06/29-18:32:55.611780 1c80             Options.enforce_single_del_contracts: true
2025/06/29-18:32:55.611785 1c80             Options.max_background_jobs: 2
2025/06/29-18:32:55.611789 1c80             Options.max_background_compactions: -1
2025/06/29-18:32:55.611793 1c80             Options.max_subcompactions: 1
2025/06/29-18:32:55.611797 1c80             Options.avoid_flush_during_shutdown: 0
2025/06/29-18:32:55.611802 1c80           Options.writable_file_max_buffer_size: 1048576
2025/06/29-18:32:55.611806 1c80             Options.delayed_write_rate : 16777216
2025/06/29-18:32:55.611810 1c80             Options.max_total_wal_size: 0
2025/06/29-18:32:55.611814 1c80             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/29-18:32:55.611818 1c80                   Options.stats_dump_period_sec: 600
2025/06/29-18:32:55.611823 1c80                 Options.stats_persist_period_sec: 600
2025/06/29-18:32:55.611827 1c80                 Options.stats_history_buffer_size: 1048576
2025/06/29-18:32:55.611831 1c80                          Options.max_open_files: -1
2025/06/29-18:32:55.611835 1c80                          Options.bytes_per_sync: 0
2025/06/29-18:32:55.611840 1c80                      Options.wal_bytes_per_sync: 0
2025/06/29-18:32:55.611844 1c80                   Options.strict_bytes_per_sync: 0
2025/06/29-18:32:55.611848 1c80       Options.compaction_readahead_size: 0
2025/06/29-18:32:55.611852 1c80                  Options.max_background_flushes: -1
2025/06/29-18:32:55.611856 1c80 Compression algorithms supported:
2025/06/29-18:32:55.611868 1c80 	kZSTD supported: 0
2025/06/29-18:32:55.611873 1c80 	kSnappyCompression supported: 0
2025/06/29-18:32:55.611877 1c80 	kBZip2Compression supported: 0
2025/06/29-18:32:55.611882 1c80 	kZlibCompression supported: 1
2025/06/29-18:32:55.611886 1c80 	kLZ4Compression supported: 1
2025/06/29-18:32:55.611891 1c80 	kXpressCompression supported: 0
2025/06/29-18:32:55.611895 1c80 	kLZ4HCCompression supported: 1
2025/06/29-18:32:55.611899 1c80 	kZSTDNotFinalCompression supported: 0
2025/06/29-18:32:55.611941 1c80 Fast CRC32 supported: Not supported on x86
2025/06/29-18:32:55.611951 1c80 DMutex implementation: std::mutex
2025/06/29-18:32:55.613729 1c80 [db\version_set.cc:5791] Recovering from manifest file: E:\napoly\server-data\db\default/MANIFEST-000250
2025/06/29-18:32:55.614178 1c80 [db\column_family.cc:621] --------------- Options for column family [default]:
2025/06/29-18:32:55.614194 1c80               Options.comparator: leveldb.BytewiseComparator
2025/06/29-18:32:55.614200 1c80           Options.merge_operator: None
2025/06/29-18:32:55.614204 1c80        Options.compaction_filter: None
2025/06/29-18:32:55.614208 1c80        Options.compaction_filter_factory: None
2025/06/29-18:32:55.614212 1c80  Options.sst_partitioner_factory: None
2025/06/29-18:32:55.614216 1c80         Options.memtable_factory: SkipListFactory
2025/06/29-18:32:55.614220 1c80            Options.table_factory: BlockBasedTable
2025/06/29-18:32:55.614274 1c80            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000020077D9C2B0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 000002007EAF59C0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-18:32:55.614286 1c80        Options.write_buffer_size: 67108864
2025/06/29-18:32:55.614291 1c80  Options.max_write_buffer_number: 2
2025/06/29-18:32:55.614295 1c80          Options.compression: LZ4
2025/06/29-18:32:55.614299 1c80                  Options.bottommost_compression: Disabled
2025/06/29-18:32:55.614303 1c80       Options.prefix_extractor: nullptr
2025/06/29-18:32:55.614307 1c80   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-18:32:55.614311 1c80             Options.num_levels: 7
2025/06/29-18:32:55.614315 1c80        Options.min_write_buffer_number_to_merge: 1
2025/06/29-18:32:55.614318 1c80     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-18:32:55.614322 1c80     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-18:32:55.614327 1c80            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-18:32:55.614331 1c80                  Options.bottommost_compression_opts.level: 32767
2025/06/29-18:32:55.614336 1c80               Options.bottommost_compression_opts.strategy: 0
2025/06/29-18:32:55.614340 1c80         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-18:32:55.614345 1c80         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-18:32:55.614349 1c80         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-18:32:55.614354 1c80                  Options.bottommost_compression_opts.enabled: false
2025/06/29-18:32:55.614358 1c80         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-18:32:55.614363 1c80         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-18:32:55.614368 1c80            Options.compression_opts.window_bits: -14
2025/06/29-18:32:55.614373 1c80                  Options.compression_opts.level: 32767
2025/06/29-18:32:55.614377 1c80               Options.compression_opts.strategy: 0
2025/06/29-18:32:55.614388 1c80         Options.compression_opts.max_dict_bytes: 0
2025/06/29-18:32:55.614395 1c80         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-18:32:55.614400 1c80         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-18:32:55.614404 1c80         Options.compression_opts.parallel_threads: 1
2025/06/29-18:32:55.614408 1c80                  Options.compression_opts.enabled: false
2025/06/29-18:32:55.614412 1c80         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-18:32:55.614416 1c80      Options.level0_file_num_compaction_trigger: 4
2025/06/29-18:32:55.614420 1c80          Options.level0_slowdown_writes_trigger: 20
2025/06/29-18:32:55.614424 1c80              Options.level0_stop_writes_trigger: 36
2025/06/29-18:32:55.614428 1c80                   Options.target_file_size_base: 67108864
2025/06/29-18:32:55.614432 1c80             Options.target_file_size_multiplier: 1
2025/06/29-18:32:55.614435 1c80                Options.max_bytes_for_level_base: 268435456
2025/06/29-18:32:55.614439 1c80 Options.level_compaction_dynamic_level_bytes: 0
2025/06/29-18:32:55.614443 1c80          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-18:32:55.614447 1c80 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-18:32:55.614451 1c80 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-18:32:55.614455 1c80 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-18:32:55.614460 1c80 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-18:32:55.614464 1c80 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-18:32:55.614469 1c80 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-18:32:55.614473 1c80 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-18:32:55.614477 1c80       Options.max_sequential_skip_in_iterations: 8
2025/06/29-18:32:55.614481 1c80                    Options.max_compaction_bytes: 1677721600
2025/06/29-18:32:55.614486 1c80   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-18:32:55.614491 1c80                        Options.arena_block_size: 1048576
2025/06/29-18:32:55.614495 1c80   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-18:32:55.614500 1c80   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-18:32:55.614504 1c80                Options.disable_auto_compactions: 0
2025/06/29-18:32:55.614514 1c80                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-18:32:55.614520 1c80                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-18:32:55.614525 1c80 Options.compaction_options_universal.size_ratio: 1
2025/06/29-18:32:55.614530 1c80 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-18:32:55.614535 1c80 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-18:32:55.614539 1c80 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-18:32:55.614544 1c80 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-18:32:55.614549 1c80 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-18:32:55.614554 1c80 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-18:32:55.614559 1c80 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-18:32:55.614566 1c80                   Options.table_properties_collectors: 
2025/06/29-18:32:55.614570 1c80                   Options.inplace_update_support: 0
2025/06/29-18:32:55.614575 1c80                 Options.inplace_update_num_locks: 10000
2025/06/29-18:32:55.614580 1c80               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-18:32:55.614585 1c80               Options.memtable_whole_key_filtering: 0
2025/06/29-18:32:55.614589 1c80   Options.memtable_huge_page_size: 0
2025/06/29-18:32:55.614593 1c80                           Options.bloom_locality: 0
2025/06/29-18:32:55.614597 1c80                    Options.max_successive_merges: 0
2025/06/29-18:32:55.614602 1c80                Options.optimize_filters_for_hits: 0
2025/06/29-18:32:55.614671 1c80                Options.paranoid_file_checks: 0
2025/06/29-18:32:55.614678 1c80                Options.force_consistency_checks: 1
2025/06/29-18:32:55.614682 1c80                Options.report_bg_io_stats: 0
2025/06/29-18:32:55.614686 1c80                               Options.ttl: 2592000
2025/06/29-18:32:55.614690 1c80          Options.periodic_compaction_seconds: 0
2025/06/29-18:32:55.614693 1c80  Options.preclude_last_level_data_seconds: 0
2025/06/29-18:32:55.614697 1c80    Options.preserve_internal_time_seconds: 0
2025/06/29-18:32:55.614701 1c80                       Options.enable_blob_files: false
2025/06/29-18:32:55.614705 1c80                           Options.min_blob_size: 0
2025/06/29-18:32:55.614710 1c80                          Options.blob_file_size: 268435456
2025/06/29-18:32:55.614714 1c80                   Options.blob_compression_type: NoCompression
2025/06/29-18:32:55.614718 1c80          Options.enable_blob_garbage_collection: false
2025/06/29-18:32:55.614722 1c80      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-18:32:55.614728 1c80 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-18:32:55.614734 1c80          Options.blob_compaction_readahead_size: 0
2025/06/29-18:32:55.614738 1c80                Options.blob_file_starting_level: 0
2025/06/29-18:32:55.614742 1c80 Options.experimental_mempurge_threshold: 0.000000
2025/06/29-18:32:55.638875 1c80 [db\version_set.cc:5842] Recovered from manifest file:E:\napoly\server-data\db\default/MANIFEST-000250 succeeded,manifest_file_number is 250, next_file_number is 252, last_sequence is 739, log_number is 245,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 245
2025/06/29-18:32:55.639101 1c80 [db\version_set.cc:5851] Column family [default] (ID 0), log number is 245
2025/06/29-18:32:55.639723 1c80 [db\db_impl\db_impl_open.cc:636] DB ID: 1ac3d8dc-4830-11f0-b4ab-244bfe56a7b1
2025/06/29-18:32:55.649349 1c80 EVENT_LOG_v1 {"time_micros": 1751207575649331, "job": 1, "event": "recovery_started", "wal_files": [249]}
2025/06/29-18:32:55.649429 1c80 [db\db_impl\db_impl_open.cc:1131] Recovering log #249 mode 2
2025/06/29-18:32:55.693421 1c80 EVENT_LOG_v1 {"time_micros": 1751207575693362, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 253, "file_size": 1375, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 754, "largest_seqno": 767, "table_properties": {"data_size": 347, "index_size": 74, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 951, "raw_average_key_size": 67, "raw_value_size": 77, "raw_average_value_size": 5, "num_data_blocks": 1, "num_entries": 14, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "LZ4", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751207575, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "1ac3d8dc-4830-11f0-b4ab-244bfe56a7b1", "db_session_id": "STZ3IMGWS96CL0XMJTM4", "orig_file_number": 253, "seqno_to_time_mapping": "N/A"}}
2025/06/29-18:32:55.713628 1c80 EVENT_LOG_v1 {"time_micros": 1751207575713604, "job": 1, "event": "recovery_finished"}
2025/06/29-18:32:55.714184 1c80 [db\version_set.cc:5304] Creating manifest 255
2025/06/29-18:32:55.809344 1c80 [file\delete_scheduler.cc:77] Deleted file E:\napoly\server-data\db\default/000249.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-18:32:55.809392 1c80 [db\db_impl\db_impl_files.cc:654] [JOB 2] Delete info log file E:\napoly\server-data\db\default//LOG.old.1751151534380760
2025/06/29-18:32:55.809727 1c80 [db\db_impl\db_impl_open.cc:2085] SstFileManager instance 000002007BD61910
2025/06/29-18:32:55.812009 1c80 DB pointer 000002007EDCEFC0
2025/06/29-18:32:55.812049 5220 [db\compaction\compaction_job.cc:1992] [default] [JOB 3] Compacting 4@0 + 1@1 files to L1, score 1.00
2025/06/29-18:32:55.812077 5220 [db\compaction\compaction_job.cc:1996] [default]: Compaction start summary: Base version 2 Base level 0, inputs: [253(1375B) 248(1375B) 243(1375B) 238(1375B)], [235(1384B)]
2025/06/29-18:32:55.812115 5220 EVENT_LOG_v1 {"time_micros": 1751207575812092, "job": 3, "event": "compaction_started", "compaction_reason": "LevelL0FilesNum", "files_L0": [253, 248, 243, 238], "files_L1": [235], "score": 1, "input_data_size": 6884, "oldest_snapshot_seqno": -1}
2025/06/29-18:32:55.814725 4bc0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-18:32:55.814770 4bc0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 0.2 total, 0.2 interval
Cumulative writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 1 writes, 1 keys, 1 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      4/4    5.37 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.043       0      0       0.0       0.0
  L1      1/1    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      5/5    6.72 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.043       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.043       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.04              0.00         1    0.043       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.2 total, 0.2 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002007EAF59C0#3616 capacity: 32.00 MB seed: 2116340078 usage: 0.91 KB table_size: 1024 occupancy: 2 collections: 1 last_copies: 0 last_secs: 0.000116 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.74 KB,0.0022471%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/29-18:32:55.878039 5220 [db\compaction\compaction_job.cc:1595] [default] [JOB 3] Generated table #258: 15 keys, 1384 bytes, temperature: kUnknown
2025/06/29-18:32:55.878242 5220 EVENT_LOG_v1 {"time_micros": 1751207575878172, "cf_name": "default", "job": 3, "event": "table_file_creation", "file_number": 258, "file_size": 1384, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 0, "largest_seqno": 0, "table_properties": {"data_size": 333, "index_size": 74, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 981, "raw_average_key_size": 65, "raw_value_size": 78, "raw_average_value_size": 5, "num_data_blocks": 1, "num_entries": 15, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "LZ4", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1749804246, "oldest_key_time": 0, "file_creation_time": 1751207575, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "1ac3d8dc-4830-11f0-b4ab-244bfe56a7b1", "db_session_id": "STZ3IMGWS96CL0XMJTM4", "orig_file_number": 258, "seqno_to_time_mapping": "N/A"}}
2025/06/29-18:32:55.945734 5220 (Original Log Time 2025/06/29-18:32:55.902494) [db\compaction\compaction_job.cc:1667] [default] [JOB 3] Compacted 4@0 + 1@1 files to L1 => 1384 bytes
2025/06/29-18:32:55.945764 5220 (Original Log Time 2025/06/29-18:32:55.945557) [db\compaction\compaction_job.cc:888] [default] compacted to: files[0 1 0 0 0 0 0] max score 0.00, MB/sec: 0.1 rd, 0.0 wr, level 1, files in(4, 1) out(1 +0 blob) MB in(0.0, 0.0 +0.0 blob) out(0.0 +0.0 blob), read-write-amplify(1.5) write-amplify(0.3) OK, records in: 71, records dropped: 56 output_compression: LZ4
2025/06/29-18:32:55.945772 5220 (Original Log Time 2025/06/29-18:32:55.945650) EVENT_LOG_v1 {"time_micros": 1751207575945581, "job": 3, "event": "compaction_finished", "compaction_time_micros": 66377, "compaction_time_cpu_micros": 0, "output_level": 1, "num_output_files": 1, "total_output_size": 1384, "num_input_records": 71, "num_output_records": 15, "num_subcompactions": 1, "output_compression": "LZ4", "num_single_delete_mismatches": 0, "num_single_delete_fallthrough": 0, "lsm_state": [0, 1, 0, 0, 0, 0, 0]}
2025/06/29-18:32:55.946472 5220 [file\delete_scheduler.cc:77] Deleted file E:\napoly\server-data\db\default/000253.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-18:32:55.946506 5220 EVENT_LOG_v1 {"time_micros": 1751207575946499, "job": 3, "event": "table_file_deletion", "file_number": 253}
2025/06/29-18:32:55.948886 5220 [file\delete_scheduler.cc:77] Deleted file E:\napoly\server-data\db\default/000248.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-18:32:55.948961 5220 EVENT_LOG_v1 {"time_micros": 1751207575948948, "job": 3, "event": "table_file_deletion", "file_number": 248}
2025/06/29-18:32:55.949439 5220 [file\delete_scheduler.cc:77] Deleted file E:\napoly\server-data\db\default/000243.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-18:32:55.949470 5220 EVENT_LOG_v1 {"time_micros": 1751207575949463, "job": 3, "event": "table_file_deletion", "file_number": 243}
2025/06/29-18:32:55.949777 5220 [file\delete_scheduler.cc:77] Deleted file E:\napoly\server-data\db\default/000238.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-18:32:55.949846 5220 EVENT_LOG_v1 {"time_micros": 1751207575949838, "job": 3, "event": "table_file_deletion", "file_number": 238}
2025/06/29-18:32:55.950155 5220 [file\delete_scheduler.cc:77] Deleted file E:\napoly\server-data\db\default/000235.sst immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-18:32:55.950179 5220 EVENT_LOG_v1 {"time_micros": 1751207575950173, "job": 3, "event": "table_file_deletion", "file_number": 235}
2025/06/29-18:42:55.815191 4bc0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-18:42:55.815233 4bc0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 600.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 13 writes, 13 keys, 13 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.043       0      0       0.0       0.0
  L1      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.3      0.1      0.0      0.07              0.00         1    0.066      71     56       0.0       0.0
 Sum      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.1      0.0      0.11              0.00         2    0.055      71     56       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0 1384.0      0.1      0.0      0.07              0.00         1    0.066      71     56       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.066      71     56       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.04              0.00         1    0.043       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 600.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002007EAF59C0#3616 capacity: 32.00 MB seed: 2116340078 usage: 0.91 KB table_size: 1024 occupancy: 2 collections: 2 last_copies: 0 last_secs: 6.6e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.74 KB,0.0022471%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/29-18:52:55.816871 4bc0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-18:52:55.816902 4bc0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 1200.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.043       0      0       0.0       0.0
  L1      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.3      0.1      0.0      0.07              0.00         1    0.066      71     56       0.0       0.0
 Sum      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.1      0.0      0.11              0.00         2    0.055      71     56       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.066      71     56       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.04              0.00         1    0.043       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1200.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002007EAF59C0#3616 capacity: 32.00 MB seed: 2116340078 usage: 0.91 KB table_size: 1024 occupancy: 2 collections: 3 last_copies: 0 last_secs: 0.000101 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.74 KB,0.0022471%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/29-19:02:55.818356 4bc0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-19:02:55.818401 4bc0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 1800.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/29-19:12:55.819779 4bc0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-19:12:55.819817 4bc0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 2400.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/29-19:22:55.821140 4bc0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-19:22:55.821285 4bc0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 3000.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/29-19:32:55.822699 4bc0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-19:32:55.822737 4bc0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 3600.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/29-19:42:55.823827 4bc0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-19:42:55.823881 4bc0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 4200.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/29-19:52:55.825753 4bc0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-19:52:55.825862 4bc0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 4800.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/29-20:02:55.827010 4bc0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-20:02:55.827071 4bc0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 5400.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/29-20:12:55.828645 4bc0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-20:12:55.828690 4bc0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 6000.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.04              0.00         1    0.043       0      0       0.0       0.0
  L1      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.3      0.1      0.0      0.07              0.00         1    0.066      71     56       0.0       0.0
 Sum      1/0    1.35 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   2.0      0.1      0.0      0.11              0.00         2    0.055      71     56       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Low      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.1      0.0      0.07              0.00         1    0.066      71     56       0.0       0.0
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.04              0.00         1    0.043       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 6000.2 total, 4800.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@000002007EAF59C0#3616 capacity: 32.00 MB seed: 2116340078 usage: 0.91 KB table_size: 1024 occupancy: 2 collections: 11 last_copies: 0 last_secs: 8.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.74 KB,0.0022471%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/06/29-20:22:55.829946 4bc0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-20:22:55.830058 4bc0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 6600.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/29-20:32:55.831237 4bc0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-20:32:55.831281 4bc0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 7200.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/29-20:42:55.832242 4bc0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-20:42:55.832270 4bc0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 7800.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/29-20:52:55.833100 4bc0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-20:52:55.833189 4bc0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 8400.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/29-21:02:55.833659 4bc0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-21:02:55.833693 4bc0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 9000.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
2025/06/29-21:12:55.834940 4bc0 [db\db_impl\db_impl.cc:1091] ------- DUMPING STATS -------
2025/06/29-21:12:55.834972 4bc0 [db\db_impl\db_impl.cc:1092] 
** DB Stats **
Uptime(secs): 9600.2 total, 600.0 interval
Cumulative writes: 14 writes, 14 keys, 14 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 14 writes, 14 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0
